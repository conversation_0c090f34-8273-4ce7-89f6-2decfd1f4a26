<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:keep="
@layout/reward_default_activity_draw_video_full_screen,
@style/rewardFullApp,
@id/draw_style1_frame,
@color/microapp_m_material_white_50,
@id/microapp_m_video_bottom_progressbar,
@id/microapp_m_video_top_layout,
@id/video_middle_layout,
@id/title,
@id/text,
@attr/windowActionBar,
@id/microapp_m_video_plugin_root,
@id/microapp_m_video_fullscreen_back,
@id/microapp_m_video_time_left_time,
@id/microapp_m_video_full_screen,
@id/microapp_m_video_retry,
@color/white,
@color/black,
@id/microapp_m_video_media_view,
@id/microapp_m_video_play,
@style/Widget.AppCompat.ProgressBar.Horizontal,
@style/TextAppearance.AppCompat.Medium,
@id/microapp_m_video_bottom_layout,
@id/microapp_m_video_seekbar_layout,
@id/microapp_m_texture_video,
@id/microapp_m_video_loading_progress,
@attr/srcCompat,
@color/microapp_m_material_white,
@style/Theme.AppCompat.Dialog,
@id/microapp_m_video_loading_retry,
@attr/windowNoTitle,
@style/Theme.AppCompat.Light.NoActionBar,
@id/microapp_m_video_time_play,
@id/microapp_m_video_seekbar,
@id/video_poster_layout,
@style/Widget.AppCompat.ProgressBar,
@attr/colorControlActivated,
@drawable/pangrowth_luckycat_shortcut
"/>