/**
 * Copyright (C) 2017 Wasabeef
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@charset "UTF-8";


html {
  height: 100%;
}

body {
  overflow: scroll;
  table-layout: fixed;
  width: 100%;
  min-height: 100%;
}
/*display: table;*/

#editor {
  outline: 0px solid transparent;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  line-height: 1.4;
}
/*display: table-cell;*/

#editor[placeholder]:empty:not(:focus):before {
  content: attr(placeholder);
  opacity: .5;
}

#editor img.full {
    display: block;
    max-width: 100%;
    margin: 5px auto;
}

#editor p {
    line-height: 1.4;
    margin: 10px 0;;
}

#editor blockquote {  
    font: 14px/22px normal helvetica, sans-serif;  
    margin-top: 10px;  
    margin-bottom: 10px;  
    margin-left: 10px;
    padding-left: 15px;  
    border-left: 3px solid #ccc;  
}